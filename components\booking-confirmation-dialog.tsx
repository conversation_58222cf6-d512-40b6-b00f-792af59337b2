"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { FileText, Printer, Check, Share2, Download, MessageSquare } from "lucide-react"
import { ReceiptPreview } from "@/components/receipt-preview"
import { Dialog as ReceiptDialog, DialogContent as ReceiptDialogContent, DialogHeader as ReceiptDialogHeader, DialogTitle as ReceiptDialogTitle, DialogDescription as ReceiptDialogDescription } from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { useState } from "react"
import { Card } from "@/components/ui/card"

interface BookingConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  bookingId: string
  onClose: () => void
  onGenerateReceipt: () => void
  senderPhone?: string
  recipientPhone?: string
  hideReceiptOption?: boolean
}

export function BookingConfirmationDialog({
  open,
  onOpenChange,
  bookingId,
  onClose,
  onGenerateReceipt,
  senderPhone = "",
  recipientPhone = "",
  hideReceiptOption = false
}: BookingConfirmationDialogProps) {
  const { toast } = useToast()
  const [shareOptions, setShareOptions] = useState({
    whatsapp: true,
    email: false
  })

  const handleGenerateLR = () => {
    // Create a simple LR content
    const lrContent = `
LR COPY
==============================
LR Number: ${bookingId}
Date: ${new Date().toLocaleDateString()}
Sender Phone: ${senderPhone}
Recipient Phone: ${recipientPhone}
==============================
This is your LR copy. Please keep it for reference.
    `;

    // Create a Blob with the LR content
    const blob = new Blob([lrContent], { type: 'text/plain' });

    // Create a URL for the Blob
    const url = URL.createObjectURL(blob);

    // Create a link element
    const link = document.createElement('a');
    link.href = url;
    link.download = `LR-${bookingId}.txt`;

    // Append the link to the body
    document.body.appendChild(link);

    // Click the link to trigger the download
    link.click();

    // Clean up
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    toast({
      title: "LR Generated",
      description: "LR has been generated and downloaded.",
    });
  }

  const [showReceiptPreview, setShowReceiptPreview] = useState(false)

  const handleShareViaWhatsApp = () => {
    // For demonstration, show a toast instead of actually opening WhatsApp
    toast({
      title: "Notification Sent",
      description: "Booking confirmation would be sent to sender and recipient via WhatsApp.",
    })

    // Show receipt preview
    setShowReceiptPreview(true)
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto overflow-x-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Check className="h-5 w-5 text-primary" />
              Booking Completed Successfully
            </DialogTitle>
            <DialogDescription>
              Your parcel has been booked successfully. LR Number: <span className="font-medium">{bookingId}</span>
            </DialogDescription>
          </DialogHeader>

          <Card className="p-4 bg-muted/30 border-dashed">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium">LR Number Pattern</h3>
              <span className="text-xs text-muted-foreground">For reference</span>
            </div>
            <p className="text-xs text-muted-foreground mb-2">
              Format: BRANCH_CODE-YYYYMMDD-XXXX
            </p>
            <div className="text-sm font-mono bg-muted p-2 rounded break-all">
              {bookingId}
            </div>
          </Card>

          <div className="flex flex-col space-y-4 py-4">
            <p className="text-sm text-muted-foreground">
              What would you like to do next?
            </p>

            <div className="flex flex-col space-y-2">
              <Button
                variant="outline"
                className="justify-start"
                onClick={handleGenerateLR}
              >
                <Download className="mr-2 h-4 w-4" />
                Generate LR Copy PDF
              </Button>

              {!hideReceiptOption && (
                <Button
                  variant="outline"
                  className="justify-start"
                  onClick={() => {
                    onGenerateReceipt()
                    setShowReceiptPreview(true)
                  }}
                >
                  <FileText className="mr-2 h-4 w-4" />
                  Generate Receipt
                </Button>
              )}

              <Button
                variant="outline"
                className="justify-start"
                onClick={() => {
                  toast({
                    title: "Print Initiated",
                    description: "Sending booking details to printer.",
                  })
                }}
              >
                <Printer className="mr-2 h-4 w-4" />
                Print Booking Details
              </Button>

              <div className="pt-2">
                <h4 className="text-sm font-medium mb-2">Share Booking Details</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="whatsapp"
                      checked={shareOptions.whatsapp}
                      onCheckedChange={(checked) =>
                        setShareOptions(prev => ({ ...prev, whatsapp: checked === true }))
                      }
                    />
                    <Label htmlFor="whatsapp" className="text-sm cursor-pointer">
                      Share via WhatsApp
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="email"
                      checked={shareOptions.email}
                      onCheckedChange={(checked) =>
                        setShareOptions(prev => ({ ...prev, email: checked === true }))
                      }
                    />
                    <Label htmlFor="email" className="text-sm cursor-pointer">
                      Share via Email
                    </Label>
                  </div>

                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              onClick={() => {
                if (shareOptions.whatsapp || shareOptions.email) {
                  handleShareViaWhatsApp();
                }
                onClose();
              }}
              className="w-full"
            >
              {(shareOptions.whatsapp || shareOptions.email) ? (
                <>
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Send Confirmation & Close
                </>
              ) : (
                "Close"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Receipt Preview Dialog */}
      <ReceiptDialog open={showReceiptPreview} onOpenChange={setShowReceiptPreview}>
        <ReceiptDialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <ReceiptDialogHeader>
            <ReceiptDialogTitle>Receipt Preview</ReceiptDialogTitle>
            <ReceiptDialogDescription>
              You can download, print or share this receipt.
            </ReceiptDialogDescription>
          </ReceiptDialogHeader>

          <ReceiptPreview
            bookingDetails={{
              id: bookingId,
              date: new Date().toLocaleDateString(),
              amount: "1500.00", // This would come from the actual booking
              customerName: "Sender Name", // This would come from the actual booking
              customerPhone: senderPhone,
              recipientName: "Recipient Name", // This would come from the actual booking
              recipientPhone: recipientPhone,
              items: [
                { name: "Package", quantity: 1, weight: "5 kg" }
              ],
              paymentMode: "Paid", // This would come from the actual booking
              deliveryType: "Standard Delivery" // This would come from the actual booking
            }}
          />

          <div className="mt-6 text-center">
            <Button
              variant="outline"
              onClick={() => setShowReceiptPreview(false)}
            >
              Close Preview
            </Button>
          </div>
        </ReceiptDialogContent>
      </ReceiptDialog>
    </>
  )
}
