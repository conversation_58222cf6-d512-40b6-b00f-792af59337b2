"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { NewVehicleLoadingPanel } from "@/components/new-vehicle-loading-panel"
import { NewVehicleReceivingPanel } from "@/components/new-vehicle-receiving-panel"

export function LoadReceiveManagement() {
  return (
    <div className="space-y-4">
      <Tabs defaultValue="load" className="space-y-4">
        <TabsList>
          <TabsTrigger value="load">Load Parcels</TabsTrigger>
          <TabsTrigger value="receive">Receive Parcels</TabsTrigger>
        </TabsList>
        <TabsContent value="load" className="space-y-4">
          <NewVehicleLoadingPanel />
        </TabsContent>
        <TabsContent value="receive" className="space-y-4">
          <NewVehicleReceivingPanel />
        </TabsContent>
      </Tabs>
    </div>
  )
}
