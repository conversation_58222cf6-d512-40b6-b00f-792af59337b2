"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Search, Truck, PackageOpen, Check, Plus, Minus, AlertCircle } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface LoadingChart {
  chart_id: number
  chart_number: string
  vehicle_id: number
  destination_branch_id: number
  loading_type: string
  status: string
  created_at: string
  vehicle: {
    registration_number: string
    vehicle_type: string
  }
  destination_branch: {
    name: string
    code: string
  }
}

interface ParcelToReceive {
  parcel_id: number
  lr_number: string
  sender_name: string
  recipient_name: string
  number_of_items: number
  current_status: string
  chart_id: number
  item_id: number
  loaded_quantity: number
  sender_branch: {
    name: string
    code: string
  }
  delivery_branch: {
    name: string
    code: string
  }
  received_quantity?: number
}

export function NewVehicleReceivingPanel() {
  const { toast } = useToast()
  
  // State management
  const [vehicleNumber, setVehicleNumber] = useState("")
  const [isSearching, setIsSearching] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [step, setStep] = useState<"search" | "lr" | "confirmation" | "complete">("search")
  
  // Data states
  const [loadingCharts, setLoadingCharts] = useState<LoadingChart[]>([])
  const [parcelsToReceive, setParcelsToReceive] = useState<ParcelToReceive[]>([])
  const [selectedParcels, setSelectedParcels] = useState<ParcelToReceive[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [receiptNumber, setReceiptNumber] = useState<string | null>(null)
  
  // LR input states
  const [currentLR, setCurrentLR] = useState("")
  const [isValidating, setIsValidating] = useState(false)
  const [validationError, setValidationError] = useState<string | null>(null)

  // Search for vehicle loading charts
  const searchVehicle = async () => {
    if (!vehicleNumber.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter a vehicle number",
        variant: "destructive",
      })
      return
    }

    setIsSearching(true)
    setError(null)

    try {
      const response = await fetch(`/api/loading-charts/by-vehicle?registration_number=${encodeURIComponent(vehicleNumber.trim())}`)
      const data = await response.json()

      if (response.ok && data.charts && data.charts.length > 0) {
        setLoadingCharts(data.charts)
        setStep("lr")
      } else {
        setError(data.error || "No active loading charts found for this vehicle")
      }
    } catch (error: any) {
      console.error('Error searching vehicle:', error)
      setError('Failed to search vehicle. Please try again.')
    } finally {
      setIsSearching(false)
    }
  }

  // Handle key press in vehicle input
  const handleVehicleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      searchVehicle()
    }
  }

  // Handle key press in LR input
  const handleLRKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      validateAndFetchLR()
    }
  }

  // Validate and fetch LR details
  const validateAndFetchLR = async () => {
    if (!currentLR.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter an LR number",
        variant: "destructive",
      })
      return
    }

    if (loadingCharts.length === 0) {
      setValidationError("No loading charts available. Please search for a vehicle first.")
      return
    }

    setIsValidating(true)
    setValidationError(null)

    try {
      // Check all loading charts for this LR
      const chartIds = loadingCharts.map(chart => chart.chart_id)
      
      const response = await fetch('/api/parcels/validate-lr-for-receiving', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          lr_number: currentLR.trim(),
          chart_ids: chartIds
        })
      })
      
      const data = await response.json()

      if (response.ok && data.valid) {
        // Check if this LR is already in the list
        const existingParcelIndex = parcelsToReceive.findIndex(p => p.lr_number === data.parcel.lr_number)

        if (existingParcelIndex !== -1) {
          toast({
            title: "LR Already Added",
            description: `LR ${data.parcel.lr_number} is already in the list. You can adjust quantities in the parcel list below.`,
            variant: "default",
          })
        } else {
          // Add the parcel to the list
          const newParcel = {
            ...data.parcel,
            received_quantity: 0 // Initialize to 0 so user must set the quantity
          }

          setParcelsToReceive(prev => [...prev, newParcel])

          toast({
            title: "Parcel Found",
            description: `LR ${data.parcel.lr_number} found. Please set the received quantity.`,
          })
        }

        setCurrentLR("")
      } else {
        setValidationError(data.message || "Invalid LR number or LR not found in any loading charts for this vehicle")
      }
    } catch (error: any) {
      console.error('Error validating LR:', error)
      setValidationError('Failed to validate LR. Please try again.')
    } finally {
      setIsValidating(false)
    }
  }

  // Handle selecting a parcel for receiving
  const handleParcelSelect = (parcel: ParcelToReceive) => {
    const isSelected = selectedParcels.some(p => p.lr_number === parcel.lr_number)

    if (isSelected) {
      setSelectedParcels(prev => prev.filter(p => p.lr_number !== parcel.lr_number))
    } else {
      setSelectedParcels(prev => [...prev, {
        ...parcel,
        received_quantity: parcel.loaded_quantity
      }])
    }
  }

  // Handle changing received quantity
  const handleQuantityChange = (lrNumber: string, change: number) => {
    setSelectedParcels(prev => prev.map(parcel => {
      if (parcel.lr_number === lrNumber) {
        const newQuantity = Math.max(1, (parcel.received_quantity || 0) + change)
        return {
          ...parcel,
          received_quantity: newQuantity
        }
      }
      return parcel
    }))
  }

  // Handle proceeding to confirmation
  const handleProceedToConfirmation = () => {
    if (selectedParcels.length === 0) {
      toast({
        title: "No Parcels Selected",
        description: "Please select at least one parcel to receive",
        variant: "destructive",
      })
      return
    }

    setStep("confirmation")
  }

  // Handle submitting the received parcels
  const handleSubmitReceived = async () => {
    if (selectedParcels.length === 0) {
      toast({
        title: "No Parcels Selected",
        description: "Please select at least one parcel to receive",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      // Group parcels by chart_id
      const parcelsByChart: Record<number, any[]> = {}

      selectedParcels.forEach(parcel => {
        if (!parcelsByChart[parcel.chart_id]) {
          parcelsByChart[parcel.chart_id] = []
        }

        parcelsByChart[parcel.chart_id].push({
          lr_number: parcel.lr_number,
          received_quantity: parcel.received_quantity
        })
      })

      // Submit each chart separately
      const results = []

      for (const [chartId, lrEntries] of Object.entries(parcelsByChart)) {
        const response = await fetch('/api/loading-charts/receive', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            chart_id: parseInt(chartId),
            lr_entries: lrEntries
          }),
        })

        const data = await response.json()

        if (response.ok) {
          results.push({
            chart_id: chartId,
            success: true,
            message: data.message
          })
        } else {
          results.push({
            chart_id: chartId,
            success: false,
            error: data.error
          })
        }
      }

      // Check if all submissions were successful
      const allSuccessful = results.every(result => result.success)

      if (allSuccessful) {
        // Generate a receipt number
        const date = new Date()
        const year = date.getFullYear().toString().slice(-2)
        const month = (date.getMonth() + 1).toString().padStart(2, "0")
        const day = date.getDate().toString().padStart(2, "0")
        const random = Math.floor(Math.random() * 10000).toString().padStart(4, "0")
        const receiptNum = `RCV${year}${month}${day}${random}`

        setReceiptNumber(receiptNum)
        setStep("complete")

        toast({
          title: "Parcels Received",
          description: `Successfully received ${selectedParcels.length} parcels`,
        })
      } else {
        const errors = results.filter(result => !result.success).map(result => result.error)
        setError(`Failed to receive some parcels: ${errors.join(", ")}`)

        toast({
          title: "Error",
          description: "Failed to receive some parcels. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      console.error('Error receiving parcels:', error)
      setError('Failed to receive parcels. Please try again.')

      toast({
        title: "Error",
        description: "Failed to receive parcels. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Reset the form
  const handleReset = () => {
    setVehicleNumber("")
    setLoadingCharts([])
    setParcelsToReceive([])
    setSelectedParcels([])
    setReceiptNumber(null)
    setError(null)
    setStep("search")
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <PackageOpen className="h-5 w-5" />
        <h2 className="text-lg font-semibold">Vehicle Receiving</h2>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Step 1: Vehicle Search */}
      {step === "search" && (
        <Card>
          <CardHeader>
            <CardTitle>Search Vehicle</CardTitle>
            <CardDescription>Enter the vehicle registration number to find loading charts</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="vehicle-number">Vehicle Registration Number</Label>
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <Input
                  id="vehicle-number"
                  placeholder="Enter vehicle number (e.g., KA01AB1234)"
                  value={vehicleNumber}
                  onChange={(e) => setVehicleNumber(e.target.value.toUpperCase())}
                  onKeyDown={handleVehicleKeyPress}
                  disabled={isSearching}
                  className="flex-1"
                />
                <Button
                  onClick={searchVehicle}
                  disabled={isSearching || !vehicleNumber.trim()}
                  className="w-full sm:w-auto"
                >
                  {isSearching ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 2: LR Entry */}
      {step === "lr" && loadingCharts.length > 0 && (
        <div className="space-y-6">
          {/* Loading Charts Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Loading Charts Found</CardTitle>
              <CardDescription>
                Found {loadingCharts.length} loading chart(s) for vehicle {vehicleNumber}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {loadingCharts.map((chart) => (
                  <div key={chart.chart_id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <span className="font-medium">{chart.chart_number}</span>
                      <p className="text-sm text-muted-foreground">
                        To: {chart.destination_branch.name} ({chart.loading_type})
                      </p>
                    </div>
                    <Badge variant="outline">{chart.status}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* LR Entry */}
          <Card>
            <CardHeader>
              <CardTitle>Enter LR Numbers</CardTitle>
              <CardDescription>Enter LR numbers to receive parcels from this vehicle</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {validationError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{validationError}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="lr-number">LR Number</Label>
                <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                  <Input
                    id="lr-number"
                    placeholder="Enter LR number"
                    value={currentLR}
                    onChange={(e) => setCurrentLR(e.target.value)}
                    onKeyDown={handleLRKeyPress}
                    disabled={isValidating}
                    className="flex-1"
                  />
                  <Button
                    onClick={validateAndFetchLR}
                    disabled={isValidating || !currentLR.trim()}
                    className="w-full sm:w-auto"
                  >
                    {isValidating ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              {/* Parcels to Receive */}
              {parcelsToReceive.length > 0 && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>Parcels to Receive</Label>
                    <Badge variant="outline">{parcelsToReceive.length} parcels</Badge>
                  </div>

                  <ScrollArea className="h-[300px] rounded-md border">
                    <div className="p-4 space-y-4">
                      {parcelsToReceive.map((parcel) => {
                        const isSelected = selectedParcels.some(p => p.lr_number === parcel.lr_number)
                        const selectedParcel = selectedParcels.find(p => p.lr_number === parcel.lr_number)

                        return (
                          <Card key={parcel.lr_number} className={`p-3 cursor-pointer transition-colors ${
                            isSelected ? 'border-primary bg-primary/5' : 'hover:bg-muted/50'
                          }`}>
                            <div className="flex items-start justify-between">
                              <div className="flex-1" onClick={() => handleParcelSelect(parcel)}>
                                <div className="flex items-center space-x-2">
                                  <span className="font-medium">{parcel.lr_number}</span>
                                  <Badge variant="outline">{parcel.loaded_quantity} items</Badge>
                                  {isSelected && <Check className="h-4 w-4 text-primary" />}
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                  From: {parcel.sender_branch?.name || 'Unknown'} •
                                  To: {parcel.delivery_branch?.name || 'Unknown'}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  Sender: {parcel.sender_name} • Recipient: {parcel.recipient_name}
                                </p>
                              </div>

                              {isSelected && (
                                <div className="flex items-center space-x-2 ml-4">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      handleQuantityChange(parcel.lr_number, -1)
                                    }}
                                    disabled={!selectedParcel?.received_quantity || selectedParcel.received_quantity <= 1}
                                  >
                                    <Minus className="h-3 w-3" />
                                  </Button>
                                  <span className="text-sm font-medium w-8 text-center">
                                    {selectedParcel?.received_quantity || 0}
                                  </span>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      handleQuantityChange(parcel.lr_number, 1)
                                    }}
                                  >
                                    <Plus className="h-3 w-3" />
                                  </Button>
                                </div>
                              )}
                            </div>
                          </Card>
                        )
                      })}
                    </div>
                  </ScrollArea>
                </div>
              )}

              {parcelsToReceive.length > 0 && (
                <div className="flex justify-between">
                  <Button variant="outline" onClick={handleReset}>
                    Start Over
                  </Button>
                  <Button
                    onClick={handleProceedToConfirmation}
                    disabled={selectedParcels.length === 0}
                  >
                    Proceed to Confirmation ({selectedParcels.length})
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Step 3: Confirmation */}
      {step === "confirmation" && (
        <Card>
          <CardHeader>
            <CardTitle>Confirm Receipt</CardTitle>
            <CardDescription>Review and confirm the parcels you are receiving</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Parcels Being Received</Label>
                <Badge variant="outline" className="font-normal">
                  {selectedParcels.length} parcels
                </Badge>
              </div>
              <ScrollArea className="h-[300px] rounded-md border">
                <div className="p-4 space-y-4">
                  {selectedParcels.map((parcel) => (
                    <Card key={parcel.lr_number} className="p-3">
                      <div className="flex items-start justify-between">
                        <div>
                          <div className="flex items-center">
                            <span className="font-medium">{parcel.lr_number}</span>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            From: {parcel.sender_branch?.name || 'Unknown'} •
                            To: {parcel.delivery_branch?.name || 'Unknown'}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Sender: {parcel.sender_name} •
                            Recipient: {parcel.recipient_name}
                          </p>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium">
                            Receiving: {parcel.received_quantity} items
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Loaded: {parcel.loaded_quantity} items
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </div>

            <div className="flex justify-between">
              <Button variant="outline" onClick={() => setStep("lr")}>
                Back to LR Entry
              </Button>
              <Button onClick={handleSubmitReceived} disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Receiving...
                  </>
                ) : (
                  "Confirm Receipt"
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 4: Complete */}
      {step === "complete" && receiptNumber && (
        <Card>
          <CardHeader>
            <CardTitle className="text-center text-green-600">Receipt Successful!</CardTitle>
            <CardDescription className="text-center">
              Parcels have been successfully received
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center space-y-2">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Check className="h-8 w-8 text-green-600" />
              </div>
              <p className="text-lg font-medium">Receipt Number: {receiptNumber}</p>
              <p className="text-sm text-muted-foreground">
                {selectedParcels.length} parcel(s) received successfully
              </p>
            </div>

            <div className="space-y-2">
              <Label>Received Parcels</Label>
              <ScrollArea className="h-[200px] rounded-md border">
                <div className="p-4 space-y-2">
                  {selectedParcels.map((parcel) => (
                    <div key={parcel.lr_number} className="flex justify-between items-center p-2 bg-muted rounded">
                      <span className="font-medium">{parcel.lr_number}</span>
                      <span className="text-sm text-muted-foreground">
                        {parcel.received_quantity} items
                      </span>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>

            <div className="flex justify-center">
              <Button onClick={handleReset}>
                Receive More Parcels
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
